module.exports = ({ env }) => ({
	'gps-extract': {
		enabled: true,
		resolve: './src/plugins/gps-extract'
	},
	email: {
		config: {
			provider: 'nodemailer',
			providerOptions: {
				host: env('SMTP_HOST', 'smtp-cp.cybersmart.co.za'),
				port: env('SMTP_PORT', 25),
				auth: {
					user: env('SMTP_USERNAME'),
					pass: env('SMTP_PASSWORD'),
				},
			},
			settings: {
				defaultFrom: '<EMAIL>',
				defaultReplyTo: '<EMAIL>',
			},
		}
	},
	menus: {
		config: {
			layouts: {
				menuItem: {
					link: [
						{
							input: {
								label: 'Icon',
								name: 'icon',
								type: 'text',
							},
							grid: {
								col: 6,
							},
						},
					],
				},
			},
		},
	},
	'schemas-to-ts': {
		enabled: false,
		config: {
			acceptedNodeEnvs: ["development", "staging"],
		}
	},
	ezforms: {
		config: {
			captchaProvider: {
				name: 'none',
			},
			notificationProviders: [
				// {
				// 	name: 'email',
				// 	enabled: true,
				// 	config: {
				// 		subject: "Cybersmart Website Contact Form Submission", // Optional
				// 		from: '<EMAIL>' // Required
				// 	}
				// },
			],
			enableFormName: true
		}
	}
});
