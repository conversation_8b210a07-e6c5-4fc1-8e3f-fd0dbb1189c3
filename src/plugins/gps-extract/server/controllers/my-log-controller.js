'use strict';

/**
 *  controller
 */

const pluginPkg = require("../../package.json");
const name = pluginPkg.strapi.name;

module.exports = ({ strapi }) => ({
	async getLogs(ctx) {
		console.log("getLogs request body:", ctx.request.body);
		const { dateFrom, dateTo, email, type } = ctx.request.body;

		const response = await strapi.plugins[name].services.myLogService.findData(dateFrom, dateTo, email, type)
		console.log("getLogs response:", response);
		ctx.type = 'application/json';
		ctx.response.body = response;

		return true;
	}
});
